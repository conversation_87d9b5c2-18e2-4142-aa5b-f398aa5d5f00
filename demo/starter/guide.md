---
title: '课程导读：解锁单词的秘密——像学汉字一样学英语！'
fonts:
  sans: '"Source Han Sans SC","Noto Sans CJK SC","Roboto","Nunito Sans","Noto Sans",ui-sans-serif,system-ui,sans-serif'
  serif: '"Source Han Serif SC","Noto Serif CJK SC","Merriweather","Noto Serif",ui-serif,Georgia,serif'
  mono: '"JetBrains Mono","Fira Code","Source Code Pro","Liberation Mono",ui-monospace,monospace'
  fallbacks: false
  local: '"Source Han Sans SC","Source Han Serif SC","JetBrains Mono"'
---

# 解锁单词的秘密
## ——像学汉字一样学英语！

<div class="pt-12">
  <span class="px-2 py-1 rounded-full bg-green-200 text-green-800">
    词根词缀单词课
  </span>
</div>
<!-- 这是一段 **备注** -->
---
layout: quote
---

# 背单词，是不是你学习路上最头疼的事？

- 单词像一片望不到边的沙漠？
- 今天背了明天忘？
- `transport` 和 `support` 傻傻分不清？
- 遇到生词只能靠运气猜？

---
layout: center
---

# 如果你也有同样的烦恼...

<h1 class="text-5xl text-green-500">恭喜你，来对地方了！</h1>

---

# 汉字的智慧：偏旁部首

让我们先看几个熟悉的汉字：

<div class="grid grid-cols-4 gap-4 text-4xl text-center p-8">
  <div class="p-4 border rounded-lg bg-teal-50 text-teal-600">河</div>
  <div class="p-4 border rounded-lg bg-teal-50 text-teal-600">湖</div>
  <div class="p-4 border rounded-lg bg-teal-50 text-teal-600">海</div>
  <div class="p-4 border rounded-lg bg-teal-50 text-teal-600">洋</div>
</div>

它们都和 **“水”** 有关，因为它们都有 <span class="font-bold text-teal-600">“氵”</span>

---

# 汉字的智慧：偏旁部首

再比如：

<div class="grid grid-cols-4 gap-4 text-4xl text-center p-8">
  <div class="p-4 border rounded-lg bg-yellow-50 text-yellow-800">树</div>
  <div class="p-4 border rounded-lg bg-yellow-50 text-yellow-800">林</div>
  <div class="p-4 border rounded-lg bg-yellow-50 text-yellow-800">森</div>
  <div class="p-4 border rounded-lg bg-yellow-50 text-yellow-800">校</div>
</div>

看到 <span class="font-bold text-yellow-800">“木”</span>，我们就能联想到 **“树木”**

---
layout: center
class: "text-center"
---

<p class="text-lg text-gray-500 mb-3">那么，回到英语...</p>
<h1 class="text-4xl font-semibold">英语单词，有没有自己的“偏旁部首”？</h1>
<hr class="w-1/4 mx-auto mt-6 border-gray-300">

---
layout: center
class: "text-center"
---

# 当然有！

<h2 class="text-4xl mt-8">就是我们的主角：</h2>
<div class="grid grid-cols-3 gap-8 mt-8">
  <div class="p-6 border rounded-lg bg-green-100 text-green-800">
    <h3 class="text-2xl font-bold">词根 (Root)</h3>
    <p class="mt-2">单词的“心脏”</p>
  </div>
  <div class="p-6 border rounded-lg bg-yellow-100 text-yellow-800">
    <h3 class="text-2xl font-bold">前缀 (Prefix)</h3>
    <p class="mt-2">改变方向或意义</p>
  </div>
  <div class="p-6 border rounded-lg bg-blue-100 text-blue-800">
    <h3 class="text-2xl font-bold">后缀 (Suffix)</h3>
    <p class="mt-2">决定词性</p>
  </div>
</div>

---
layout: section
---

# 见证奇迹的时刻

---

# 核心词根：<span class="root-word" >port</span>


<div class="grid grid-cols-2 gap-8 items-center">
<div>

<div class="root-showcase">
  <div class="root-word">port</div>
  <div class="root-meaning">意思是“拿，搬运”</div>
</div>

</div>
<div>

<div class="word-family-grid mt-8">
  <div class="word-card my-2">trans + port = <span v-click class="font-bold text-green-500">transport</span> <span v-click>(运输)</span></div>
  <div class="word-card my-2">ex + port = <span  v-click class="font-bold text-green-500">export</span> <span v-click>(出口)</span></div>
  <div class="word-card my-2">im + port = <span  v-click class="font-bold text-green-500">import</span> <span v-click>(进口)</span></div>
  <div class="word-card my-2">sup + port = <span  v-click class="font-bold text-green-500">support</span> <span v-click>(支持)</span></div>
  <div class="word-card my-2">re + port = <span  v-click class="font-bold text-green-500">report</span> <span v-click>(报告)</span></div>
</div>

</div>
</div>

---
layout: section
---

# 前后缀的威力

---

# 前缀：改变词义

<div class="grid grid-cols-3 gap-4 text-center">
  <div>
    <h3 class="text-2xl font-bold text-red-500">un- (不)</h3>
    <p class="text-xl mt-2">happy → <span class="text-red-500 font-bold">un</span>happy</p>
    <p class="text-xl mt-2">lock → <span class="text-red-500 font-bold">un</span>lock</p>
  </div>
  <div>
    <h3 class="text-2xl font-bold text-blue-500">re- (再)</h3>
    <p class="text-xl mt-2">write → <span class="text-blue-500 font-bold">re</span>write</p>
    <p class="text-xl mt-2">play → <span class="text-blue-500 font-bold">re</span>play</p>
  </div>
  <div>
    <h3 class="text-2xl font-bold text-purple-500">dis- (分离)</h3>
    <p class="text-xl mt-2">connect → <span class="text-purple-500 font-bold">dis</span>connect</p>
    <p class="text-xl mt-2">appear → <span class="text-purple-500 font-bold">dis</span>appear</p>
  </div>
</div>

---

# 后缀：改变词性

<div class="grid grid-cols-2 gap-8">
  <div class="p-4 border rounded-lg bg-gray-50">
    <h3 class="text-2xl font-bold text-center">act (行动)</h3>
    <ul class="mt-4 text-xl space-y-2">
      <li>act<span class="text-green-600 font-bold">-ion</span> → (名) 动作</li>
      <li>act<span class="text-blue-600 font-bold">-ive</span> → (形) 活跃的</li>
      <li>active<span class="text-orange-600 font-bold">-ly</span> → (副) 积极地</li>
    </ul>
  </div>
  <div class="p-4 border rounded-lg bg-gray-50">
    <h3 class="text-2xl font-bold text-center">create (创造)</h3>
    <ul class="mt-4 text-xl space-y-2">
      <li>create<span class="text-green-600 font-bold">-ion</span> → (名) 创造</li>
      <li>creat<span class="text-blue-600 font-bold">-ive</span> → (形) 有创造力的</li>
      <li>creative<span class="text-orange-600 font-bold">-ly</span> → (副) 创造性地</li>
    </ul>
  </div>
</div>

---
layout: section
---

# 构词法：单词的更多构造方式

---

# 1. 派生 (Derivation)

<div class="text-xl">
通过加前缀或后缀，从一个词（词根）派生出新词。这是最常见的构词方式。

<div class="mt-6 grid grid-cols-2 gap-4">
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">legal (合法的) → <span v-click > <span class="font-bold text-red-500">il</span>legal (不合法的)</span></p>
  </div>
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">danger (危险) → <span v-click >  danger<span class="font-bold text-blue-500">ous</span> (危险的) </span></p>
  </div>
</div>
</div>

---

# 2. 合成 (Compounding)

<div class="text-xl">
将两个或多个独立的词合并成一个新词。

<div class="mt-6 grid grid-cols-2 gap-4">
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">black (黑色的) + board (木板) → <span v-click > <span class="font-bold text-green-500">blackboard</span> (黑板)</span></p>
  </div>
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">sun (太阳) + flower (花) → <span v-click > <span class="font-bold text-green-500">sunflower</span> (向日葵)</span></p>
  </div>
</div>
</div>

---

# 3. 混合 (Blending)

<div class="text-xl">
将两个词的一部分混合在一起，创造一个新词。

<div class="mt-6 grid grid-cols-2 gap-4">
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">smoke (烟) + fog (雾) → <span v-click > <span class="font-bold text-purple-500">smog</span> (烟雾)</span></p>
  </div>
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">breakfast (早餐) + lunch (午餐) → <span v-click > <span class="font-bold text-purple-500">brunch</span> (早午餐)</span></p>
  </div>
</div>
</div>

---

# 4. 截短 (Clipping)

<div class="text-xl">
缩短一个较长的单词，使其更易于使用。

<div class="mt-6 grid grid-cols-2 gap-4">
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">telephone (电话) → <span v-click class="font-bold text-orange-500">phone</span></p>
  </div>
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">advertisement (广告) → <span v-click class="font-bold text-orange-500">ad</span></p>
  </div>
</div>
</div>

---

# 5. 缩写 (Acronyms & Initialisms)

<div class="text-xl">
由一个短语中每个词的首字母组成。

<div class="mt-6 grid grid-cols-2 gap-4">
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">National Aeronautics and Space Administration → <span v-click class="font-bold text-indigo-500">NASA</span></p>
  </div>
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">World Health Organization → <span v-click class="font-bold text-indigo-500">WHO</span></p>
  </div>
</div>
</div>

---

# 6. 转化 (Conversion)

<div class="text-xl">
一个单词从一种词性转变为另一种词性，而词形不变。

<div class="mt-6 grid grid-cols-2 gap-4">
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">email (名词) → <span v-click class="font-bold text-teal-500">email</span> <span v-click>(动词)</span></p>
    <p v-click class="text-center text-base text-gray-500">"Send me an email." vs "I will <span class="font-bold text-teal-500">email</span> you."</p>
  </div>
  <div class="p-4 border rounded-lg bg-gray-50">
    <p class="text-center">water (名词) → <span v-click class="font-bold text-teal-500">water</span> <span v-click>(动词)</span></p>
    <p  v-click class="text-center text-base text-gray-500">"A glass of water." vs "<span class="font-bold text-teal-500">Water</span> the plants."</p>
  </div>
</div>
</div>

---
layout: section
---

# 为什么要这样学？

---

# 四大核心优势

1.  **高效记忆**
    <br>
    <span class="text-xl">从死记硬背，变为逻辑拼接，效率指数级提升。</span>
2.  **举一反三**
    <br>
    <span class="text-xl">掌握一个词根，解锁一串单词，词汇量成倍增长。</span>
3.  **猜词能力**
    <br>
    <span class="text-xl">遇到生词，也能像侦探一样推断出大意。</span>
4.  **深刻理解**
    <br>
    <span class="text-xl">你不再是“认识”单词，而是“理解”它，永生难忘。</span>

---
layout: section
---

# 课程展望

---

# 这门课的目标

不是让你背下几千个孤立的单词...

而是帮你建立一套属于自己的
<h1 class="text-5xl text-green-500 mt-4">“单词思维系统”</h1>

---
layout: center
class: text-center
---

# 准备好了吗？

## 让我们一起，开启这场奇妙而高效的单词探险之旅吧！
