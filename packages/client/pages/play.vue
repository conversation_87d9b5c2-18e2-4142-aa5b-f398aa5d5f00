<script setup lang="ts">
import { useStyleTag } from '@vueuse/core'
import { computed, ref, shallowRef, watch } from 'vue'
import { useDrawings } from '../composables/useDrawings'
import { useHideCursorIdle } from '../composables/useHideCursorIdle'
import { useNav } from '../composables/useNav'
import { useSwipeControls } from '../composables/useSwipeControls'
import { useWakeLock } from '../composables/useWakeLock'
import Controls from '../internals/Controls.vue'
import NavControls from '../internals/NavControls.vue'
import PresenterMouse from '../internals/PresenterMouse.vue'
import SlideContainer from '../internals/SlideContainer.vue'
import SlidesShow from '../internals/SlidesShow.vue'
import { onContextMenu } from '../logic/contextMenu'
import { registerShortcuts } from '../logic/shortcuts'
import { editorHeight, editorWidth, isEditorVertical, isScreenVertical, showEditor, viewerCssFilter, viewerCssFilterDefaults } from '../state'

const { next, prev, isPrintMode, isPlaying, isEmbedded } = useNav()
const { isDrawing, drawingEnabled } = useDrawings()

const root = ref<HTMLDivElement>()
const showNavInDrawingMode = ref(false)

// 在绘图模式下，只允许明确的手指点击显示导航栏
function onControlsAreaTouch(e: TouchEvent) {
  if (!drawingEnabled.value) {
    return // 非绘图模式，使用默认行为
  }

  // 在绘图模式下，阻止所有触摸事件的默认行为
  e.preventDefault()
  e.stopPropagation()

  // 只有明确的手指触摸才显示菜单
  const touch = e.touches[0]
  if (touch) {
    // 检查是否是手指触摸（没有压力或压力很小，且不是stylus类型）
    const hasLowPressure = !touch.force || touch.force < 0.1
    const isNotStylus = touch.touchType !== 'stylus'

    if (hasLowPressure && isNotStylus) {
      showNavInDrawingMode.value = !showNavInDrawingMode.value
    }
  }
}

// 点击画布区域隐藏导航栏
function onCanvasTouch(e: TouchEvent) {
  if (drawingEnabled.value && showNavInDrawingMode.value) {
    const touch = e.touches[0]
    const isFingerTouch = touch &&
      (touch.touchType === 'direct' ||
       touch.touchType === undefined ||
       (touch.touchType !== 'stylus' && (!touch.force || touch.force < 0.1)))

    if (isFingerTouch) {
      showNavInDrawingMode.value = false
    }
  }
}

// 当绘图模式关闭时，隐藏导航栏
watch(drawingEnabled, (enabled) => {
  if (!enabled) {
    showNavInDrawingMode.value = false
  }
})

function onClick(e: MouseEvent) {
  if (showEditor.value)
    return

  if (e.button === 0 && (e.target as HTMLElement)?.id === 'slide-container') {
    // click right to next, left to previous
    if ((e.pageX / window.innerWidth) > 0.5)
      next()
    else
      prev()
  }
}

useSwipeControls(root)
registerShortcuts()
if (__SLIDEV_FEATURE_WAKE_LOCK__)
  useWakeLock()
useHideCursorIdle(computed(() => isPlaying.value && !isEmbedded.value && !showEditor.value))

if (import.meta.hot) {
  useStyleTag(computed(() => showEditor.value
    ? `
    vite-error-overlay {
      --width: calc(100vw - ${isEditorVertical.value ? 0 : editorWidth.value}px);
      --height: calc(100vh - ${isEditorVertical.value ? editorHeight.value : 0}px);
      position: fixed;
      left: 0;
      top: 0;
      width: calc(var(--width) / var(--slidev-slide-scale));
      height: calc(var(--height) / var(--slidev-slide-scale));
      transform-origin: top left;
      transform: scale(var(--slidev-slide-scale));
    }`
    : '',
  ))
}

const persistNav = computed(() => isScreenVertical.value || showEditor.value)

const SideEditor = shallowRef<any>()
if (__DEV__ && __SLIDEV_FEATURE_EDITOR__)
  import('../internals/SideEditor.vue').then(v => SideEditor.value = v.default)

const contentStyle = computed(() => {
  let filter = ''

  if (viewerCssFilter.value.brightness !== viewerCssFilterDefaults.brightness)
    filter += `brightness(${viewerCssFilter.value.brightness}) `
  if (viewerCssFilter.value.contrast !== viewerCssFilterDefaults.contrast)
    filter += `contrast(${viewerCssFilter.value.contrast}) `
  if (viewerCssFilter.value.sepia !== viewerCssFilterDefaults.sepia)
    filter += `sepia(${viewerCssFilter.value.sepia}) `
  if (viewerCssFilter.value.hueRotate !== viewerCssFilterDefaults.hueRotate)
    filter += `hue-rotate(${viewerCssFilter.value.hueRotate}deg) `
  if (viewerCssFilter.value.invert)
    filter += 'invert(1) '

  return {
    filter,
  }
})
</script>

<template>
  <div
    id="page-root" ref="root" class="grid"
    :class="isEditorVertical ? 'grid-rows-[1fr_max-content]' : 'grid-cols-[1fr_max-content]'"
  >
    <SlideContainer
      :style="{ background: 'var(--slidev-slide-container-background, black)' }"
      is-main
      :content-style="contentStyle"
      @pointerdown="onClick"
      @contextmenu="onContextMenu"
      @touchstart="onCanvasTouch"
    >
      <template #default>
        <SlidesShow render-context="slide" />
        <PresenterMouse />
      </template>
      <template #controls>
        <div
          v-if="!isPrintMode"
          class="absolute bottom-0 left-0 transition duration-300"
          :class="[
            persistNav ? '!opacity-100 right-0' : 'p-2',
            drawingEnabled ?
              (showNavInDrawingMode ? '!opacity-100' : 'opacity-0 pointer-events-none') :
              'opacity-0 hover:opacity-100 focus-within:opacity-100 focus-visible:opacity-100',
          ]"
          @touchstart.stop="onControlsAreaTouch"
          @mouseenter.stop="(e) => drawingEnabled && e.preventDefault()"
          @pointerenter.stop="(e) => drawingEnabled && e.preventDefault()"
          @mouseover.stop="(e) => drawingEnabled && e.preventDefault()"
          @pointerover.stop="(e) => drawingEnabled && e.preventDefault()"
        >
          <NavControls :persist="persistNav" />
        </div>
      </template>
    </SlideContainer>
    <SideEditor v-if="SideEditor && showEditor" :resize="true" />
  </div>
  <Controls v-if="!isPrintMode" />
  <div id="twoslash-container" />
</template>

<style>
/* 在绘图模式下完全禁用底部控制栏的hover效果 */
body.slidev-drawing .absolute.bottom-0 {
  pointer-events: none !important;
}

body.slidev-drawing .absolute.bottom-0.opacity-100 {
  pointer-events: auto !important;
}

body.slidev-drawing .absolute.bottom-0:hover {
  opacity: 0 !important;
}

/* 确保在绘图模式下hover类不生效 */
body.slidev-drawing .hover\\:opacity-100:hover {
  opacity: 0 !important;
}
</style>
