<script setup lang="ts">
import { useStyleTag } from '@vueuse/core'
import { computed, ref, shallowRef, watch } from 'vue'
import { useDrawings } from '../composables/useDrawings'
import { useHideCursorIdle } from '../composables/useHideCursorIdle'
import { useNav } from '../composables/useNav'
import { useSwipeControls } from '../composables/useSwipeControls'
import { useWakeLock } from '../composables/useWakeLock'
import Controls from '../internals/Controls.vue'
import NavControls from '../internals/NavControls.vue'
import PresenterMouse from '../internals/PresenterMouse.vue'
import SlideContainer from '../internals/SlideContainer.vue'
import SlidesShow from '../internals/SlidesShow.vue'
import { onContextMenu } from '../logic/contextMenu'
import { registerShortcuts } from '../logic/shortcuts'
import { editorHeight, editorWidth, isEditorVertical, isScreenVertical, showEditor, viewerCssFilter, viewerCssFilterDefaults } from '../state'

const { next, prev, isPrintMode, isPlaying, isEmbedded } = useNav()
const { isDrawing, drawingEnabled } = useDrawings()

const root = ref<HTMLDivElement>()
const showNavInDrawingMode = ref(false)

// 在绘图模式下，只允许手指触摸显示导航栏，阻止笔触发
function onControlsAreaTouch(e: TouchEvent) {
  if (!drawingEnabled.value) return

  // 检查是否是手指触摸（非笔触）
  const touch = e.touches[0]
  // 判断是否为手指触摸：touchType不是stylus，且没有压力值或压力值很小
  const isFingerTouch = touch &&
    (touch.touchType === 'direct' ||
     touch.touchType === undefined ||
     (touch.touchType !== 'stylus' && (!touch.force || touch.force < 0.1)))

  if (isFingerTouch) {
    // 这是手指触摸，允许显示导航栏
    showNavInDrawingMode.value = true
  } else {
    // 这是笔触，阻止显示
    e.preventDefault()
    e.stopPropagation()
  }
}

// 点击画布区域隐藏导航栏
function onCanvasTouch(e: TouchEvent) {
  if (drawingEnabled.value && showNavInDrawingMode.value) {
    const touch = e.touches[0]
    const isFingerTouch = touch &&
      (touch.touchType === 'direct' ||
       touch.touchType === undefined ||
       (touch.touchType !== 'stylus' && (!touch.force || touch.force < 0.1)))

    if (isFingerTouch) {
      showNavInDrawingMode.value = false
    }
  }
}

// 当绘图模式关闭时，隐藏导航栏
watch(drawingEnabled, (enabled) => {
  if (!enabled) {
    showNavInDrawingMode.value = false
  }
})

function onClick(e: MouseEvent) {
  if (showEditor.value)
    return

  if (e.button === 0 && (e.target as HTMLElement)?.id === 'slide-container') {
    // click right to next, left to previous
    if ((e.pageX / window.innerWidth) > 0.5)
      next()
    else
      prev()
  }
}

useSwipeControls(root)
registerShortcuts()
if (__SLIDEV_FEATURE_WAKE_LOCK__)
  useWakeLock()
useHideCursorIdle(computed(() => isPlaying.value && !isEmbedded.value && !showEditor.value))

if (import.meta.hot) {
  useStyleTag(computed(() => showEditor.value
    ? `
    vite-error-overlay {
      --width: calc(100vw - ${isEditorVertical.value ? 0 : editorWidth.value}px);
      --height: calc(100vh - ${isEditorVertical.value ? editorHeight.value : 0}px);
      position: fixed;
      left: 0;
      top: 0;
      width: calc(var(--width) / var(--slidev-slide-scale));
      height: calc(var(--height) / var(--slidev-slide-scale));
      transform-origin: top left;
      transform: scale(var(--slidev-slide-scale));
    }`
    : '',
  ))
}

const persistNav = computed(() => isScreenVertical.value || showEditor.value)

const SideEditor = shallowRef<any>()
if (__DEV__ && __SLIDEV_FEATURE_EDITOR__)
  import('../internals/SideEditor.vue').then(v => SideEditor.value = v.default)

const contentStyle = computed(() => {
  let filter = ''

  if (viewerCssFilter.value.brightness !== viewerCssFilterDefaults.brightness)
    filter += `brightness(${viewerCssFilter.value.brightness}) `
  if (viewerCssFilter.value.contrast !== viewerCssFilterDefaults.contrast)
    filter += `contrast(${viewerCssFilter.value.contrast}) `
  if (viewerCssFilter.value.sepia !== viewerCssFilterDefaults.sepia)
    filter += `sepia(${viewerCssFilter.value.sepia}) `
  if (viewerCssFilter.value.hueRotate !== viewerCssFilterDefaults.hueRotate)
    filter += `hue-rotate(${viewerCssFilter.value.hueRotate}deg) `
  if (viewerCssFilter.value.invert)
    filter += 'invert(1) '

  return {
    filter,
  }
})
</script>

<template>
  <div
    id="page-root" ref="root" class="grid"
    :class="isEditorVertical ? 'grid-rows-[1fr_max-content]' : 'grid-cols-[1fr_max-content]'"
  >
    <SlideContainer
      :style="{ background: 'var(--slidev-slide-container-background, black)' }"
      is-main
      :content-style="contentStyle"
      @pointerdown="onClick"
      @contextmenu="onContextMenu"
      @touchstart="onCanvasTouch"
    >
      <template #default>
        <SlidesShow render-context="slide" />
        <PresenterMouse />
      </template>
      <template #controls>
        <div
          v-if="!isPrintMode"
          class="absolute bottom-0 left-0 transition duration-300 opacity-0"
          :class="[
            persistNav ? '!opacity-100 right-0' : 'opacity-0 p-2',
            drawingEnabled ? (showNavInDrawingMode ? '!opacity-100' : 'pointer-events-none') : 'hover:opacity-100 focus-within:opacity-100 focus-visible:opacity-100',
          ]"
          :style="drawingEnabled ? { pointerEvents: showNavInDrawingMode.value ? 'auto' : 'none' } : {}"
          @touchstart="onControlsAreaTouch"
          @pointerenter="(e) => drawingEnabled && e.pointerType === 'pen' && e.preventDefault()"
          @mouseenter="(e) => drawingEnabled && e.preventDefault()"
        >
          <NavControls :persist="persistNav" />
        </div>
      </template>
    </SlideContainer>
    <SideEditor v-if="SideEditor && showEditor" :resize="true" />
  </div>
  <Controls v-if="!isPrintMode" />
  <div id="twoslash-container" />
</template>

<style scoped>
/* 在绘图模式下禁用所有hover效果 */
.slidev-drawing .absolute.bottom-0:not(.opacity-100) {
  pointer-events: none !important;
}

.slidev-drawing .absolute.bottom-0:not(.opacity-100):hover {
  opacity: 0 !important;
}
</style>
